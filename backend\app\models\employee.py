# =============================================================================
# MODÈLE EMPLOYÉ - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Modèle pour les employés avec badges d'accès RFID.
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum
from app.models.base import BaseModel


class EmployeeStatus(str, Enum):
    """Statuts employé."""
    ACTIVE = "active"                       # Actif
    INACTIVE = "inactive"                   # Inactif
    SUSPENDED = "suspended"                 # Suspendu
    TERMINATED = "terminated"               # Licencié


class AccessLevel(str, Enum):
    """Niveaux d'accès."""
    BASIC = "basic"                         # Accès de base
    EXTENDED = "extended"                   # Accès étendu
    FULL = "full"                          # Accès complet
    RESTRICTED = "restricted"               # Accès restreint


class Employee(BaseModel):
    """
    Modèle employé pour la gestion des accès RFID.
    
    Représente les employés de l'entreprise qui possèdent des badges
    d'accès RFID pour entrer dans les locaux.
    """
    __tablename__ = "employees"
    
    # ==========================================================================
    # INFORMATIONS PERSONNELLES
    # ==========================================================================
    
    # Numéro d'employé unique
    employee_number = Column(String(20), unique=True, index=True, nullable=False)
    
    # Nom complet
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    
    # Email professionnel
    email = Column(String(255), unique=True, index=True, nullable=True)
    
    # Téléphone
    phone = Column(String(20), nullable=True)
    
    # Département
    department = Column(String(100), nullable=True)
    
    # Poste/Fonction
    job_title = Column(String(100), nullable=True)
    
    # Manager/Superviseur
    manager = Column(String(100), nullable=True)
    
    # ==========================================================================
    # INFORMATIONS BADGE RFID
    # ==========================================================================
    
    # ID de la carte RFID (unique)
    card_id = Column(String(50), unique=True, index=True, nullable=False)
    
    # Numéro de série de la carte
    card_serial = Column(String(50), nullable=True)
    
    # Type de carte (MIFARE, etc.)
    card_type = Column(String(50), default="MIFARE_DESFire_EV2", nullable=False)
    
    # Date d'émission de la carte
    card_issued_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Date d'expiration de la carte
    card_expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Carte active
    card_active = Column(Boolean, default=True, nullable=False)
    
    # Raison de désactivation de la carte
    card_deactivation_reason = Column(String(255), nullable=True)
    
    # ==========================================================================
    # CONTRÔLE D'ACCÈS
    # ==========================================================================
    
    # Statut de l'employé
    status = Column(SQLEnum(EmployeeStatus), nullable=False, default=EmployeeStatus.ACTIVE)
    
    # Niveau d'accès
    access_level = Column(SQLEnum(AccessLevel), nullable=False, default=AccessLevel.BASIC)
    
    # Date de début d'accès
    access_start_date = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Date de fin d'accès
    access_end_date = Column(DateTime(timezone=True), nullable=True)
    
    # Horaires d'accès autorisés (JSON)
    access_schedule = Column(Text, nullable=True)  # JSON: {"monday": {"start": "08:00", "end": "18:00"}, ...}
    
    # Zones d'accès autorisées (JSON)
    authorized_zones = Column(Text, nullable=True)  # JSON: ["zone_1", "zone_2", ...]
    
    # Lecteurs autorisés (JSON)
    authorized_readers = Column(Text, nullable=True)  # JSON: ["reader_1", "reader_2", ...]
    
    # ==========================================================================
    # INFORMATIONS CONTRACTUELLES
    # ==========================================================================
    
    # Date d'embauche
    hire_date = Column(DateTime(timezone=True), nullable=True)
    
    # Date de fin de contrat
    contract_end_date = Column(DateTime(timezone=True), nullable=True)
    
    # Type de contrat
    contract_type = Column(String(50), nullable=True)  # CDI, CDD, Stage, etc.
    
    # ==========================================================================
    # SÉCURITÉ ET MONITORING
    # ==========================================================================
    
    # Nombre de tentatives d'accès refusées
    failed_access_attempts = Column(Integer, default=0, nullable=False)
    
    # Dernière tentative d'accès refusée
    last_failed_access_at = Column(DateTime(timezone=True), nullable=True)
    
    # Dernière connexion réussie
    last_successful_access_at = Column(DateTime(timezone=True), nullable=True)
    
    # Dernière localisation (lecteur)
    last_location = Column(String(100), nullable=True)
    
    # Présent sur site
    is_present = Column(Boolean, default=False, nullable=False)
    
    # Heure d'entrée aujourd'hui
    today_entry_time = Column(DateTime(timezone=True), nullable=True)
    
    # Heure de sortie aujourd'hui
    today_exit_time = Column(DateTime(timezone=True), nullable=True)
    
    # ==========================================================================
    # INFORMATIONS SUPPLÉMENTAIRES
    # ==========================================================================
    
    # Photo de l'employé (chemin vers le fichier)
    photo_path = Column(String(255), nullable=True)
    
    # Notes administratives
    notes = Column(Text, nullable=True)
    
    # Données personnalisées (JSON)
    custom_data = Column(Text, nullable=True)
    
    # ==========================================================================
    # RELATIONS
    # ==========================================================================
    
    # Logs d'accès de cet employé
    access_logs = relationship("AccessLog", back_populates="employee", lazy="dynamic")
    
    # Alertes concernant cet employé
    alerts = relationship("Alert", back_populates="employee", lazy="dynamic")
    
    # ==========================================================================
    # PROPRIÉTÉS
    # ==========================================================================
    
    @property
    def full_name(self) -> str:
        """Nom complet de l'employé."""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def is_access_active(self) -> bool:
        """Vérifie si l'accès est actif."""
        now = func.now()
        return (
            self.is_active and
            self.status == EmployeeStatus.ACTIVE and
            self.card_active and
            self.access_start_date <= now and
            (self.access_end_date is None or self.access_end_date >= now) and
            (self.card_expires_at is None or self.card_expires_at >= now)
        )
    
    @property
    def is_card_expired(self) -> bool:
        """Vérifie si la carte est expirée."""
        if self.card_expires_at is None:
            return False
        return self.card_expires_at < func.now()
    
    @property
    def days_until_card_expiry(self) -> int:
        """Nombre de jours avant expiration de la carte."""
        if self.card_expires_at is None:
            return -1
        
        from datetime import datetime
        now = datetime.utcnow()
        delta = self.card_expires_at - now
        return delta.days
    
    # ==========================================================================
    # MÉTHODES
    # ==========================================================================
    
    def deactivate_card(self, reason: str = None):
        """Désactive la carte d'accès."""
        self.card_active = False
        self.card_deactivation_reason = reason
    
    def activate_card(self):
        """Active la carte d'accès."""
        self.card_active = True
        self.card_deactivation_reason = None
    
    def suspend_access(self):
        """Suspend l'accès de l'employé."""
        self.status = EmployeeStatus.SUSPENDED
        self.deactivate_card("Accès suspendu")
    
    def restore_access(self):
        """Restaure l'accès de l'employé."""
        self.status = EmployeeStatus.ACTIVE
        self.activate_card()
    
    def terminate_employment(self):
        """Termine l'emploi et désactive tous les accès."""
        self.status = EmployeeStatus.TERMINATED
        self.deactivate_card("Emploi terminé")
        self.access_end_date = func.now()
        self.is_present = False
    
    def update_presence(self, is_entering: bool, location: str = None):
        """Met à jour le statut de présence."""
        now = func.now()
        
        if is_entering:
            self.is_present = True
            self.today_entry_time = now
            self.last_successful_access_at = now
        else:
            self.is_present = False
            self.today_exit_time = now
        
        if location:
            self.last_location = location
    
    def increment_failed_access(self):
        """Incrémente le compteur d'accès refusés."""
        self.failed_access_attempts += 1
        self.last_failed_access_at = func.now()
        
        # Suspension automatique après 10 tentatives
        if self.failed_access_attempts >= 10:
            self.suspend_access()
    
    def reset_failed_access(self):
        """Remet à zéro le compteur d'accès refusés."""
        self.failed_access_attempts = 0
        self.last_failed_access_at = None
    
    def __repr__(self):
        return f"<Employee(id={self.id}, number='{self.employee_number}', name='{self.full_name}')>"
