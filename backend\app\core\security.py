# =============================================================================
# SÉCURITÉ ET AUTHENTIFICATION - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Gestion de la sécurité, authentification JWT et hachage des mots de passe.
"""

from datetime import datetime, timedelta
from typing import Optional, Union, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import secrets
import hashlib
import hmac
from enum import Enum

from app.core.config import settings

# =============================================================================
# CONFIGURATION SÉCURITÉ
# =============================================================================

# Context pour le hachage des mots de passe
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Bearer token pour JWT
security = HTTPBearer()

# =============================================================================
# ÉNUMÉRATIONS
# =============================================================================

class UserRole(str, Enum):
    """Rôles utilisateur du système."""
    OPERATOR = "operator"                    # Opérateur - Surveillance
    ADMIN = "admin"                         # Administrateur - Gestion
    SUPER_ADMIN = "super_admin"             # Super Admin - Accès complet

class TokenType(str, Enum):
    """Types de tokens JWT."""
    ACCESS = "access"
    REFRESH = "refresh"

# =============================================================================
# GESTION DES MOTS DE PASSE
# =============================================================================

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Vérifie un mot de passe en clair contre son hash.
    
    Args:
        plain_password: Mot de passe en clair
        hashed_password: Mot de passe haché
        
    Returns:
        bool: True si le mot de passe est correct
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Génère le hash d'un mot de passe.
    
    Args:
        password: Mot de passe en clair
        
    Returns:
        str: Hash du mot de passe
    """
    return pwd_context.hash(password)


def generate_password(length: int = 12) -> str:
    """
    Génère un mot de passe aléatoire sécurisé.
    
    Args:
        length: Longueur du mot de passe
        
    Returns:
        str: Mot de passe généré
    """
    return secrets.token_urlsafe(length)


# =============================================================================
# GESTION DES TOKENS JWT
# =============================================================================

def create_access_token(
    subject: Union[str, Any],
    expires_delta: Optional[timedelta] = None,
    user_role: Optional[UserRole] = None,
    additional_claims: Optional[dict] = None
) -> str:
    """
    Crée un token d'accès JWT.
    
    Args:
        subject: Sujet du token (généralement user_id)
        expires_delta: Durée de validité personnalisée
        user_role: Rôle de l'utilisateur
        additional_claims: Claims supplémentaires
        
    Returns:
        str: Token JWT encodé
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "type": TokenType.ACCESS.value,
        "iat": datetime.utcnow(),
    }
    
    if user_role:
        to_encode["role"] = user_role.value
    
    if additional_claims:
        to_encode.update(additional_claims)
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def create_refresh_token(
    subject: Union[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    Crée un token de rafraîchissement JWT.
    
    Args:
        subject: Sujet du token (généralement user_id)
        expires_delta: Durée de validité personnalisée
        
    Returns:
        str: Token JWT encodé
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            days=settings.REFRESH_TOKEN_EXPIRE_DAYS
        )
    
    to_encode = {
        "exp": expire,
        "sub": str(subject),
        "type": TokenType.REFRESH.value,
        "iat": datetime.utcnow(),
    }
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_token(token: str, token_type: TokenType = TokenType.ACCESS) -> Optional[dict]:
    """
    Vérifie et décode un token JWT.
    
    Args:
        token: Token JWT à vérifier
        token_type: Type de token attendu
        
    Returns:
        dict: Payload du token si valide, None sinon
    """
    try:
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        
        # Vérification du type de token
        if payload.get("type") != token_type.value:
            return None
            
        return payload
        
    except JWTError:
        return None


def get_token_subject(token: str) -> Optional[str]:
    """
    Extrait le sujet d'un token JWT.
    
    Args:
        token: Token JWT
        
    Returns:
        str: Sujet du token si valide, None sinon
    """
    payload = verify_token(token)
    if payload:
        return payload.get("sub")
    return None


# =============================================================================
# DÉPENDANCES FASTAPI
# =============================================================================

async def get_current_user_token(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> dict:
    """
    Dépendance FastAPI pour extraire et vérifier le token JWT.
    
    Args:
        credentials: Credentials HTTP Bearer
        
    Returns:
        dict: Payload du token
        
    Raises:
        HTTPException: Si le token est invalide
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Token d'authentification invalide",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception
            
        return payload
        
    except JWTError:
        raise credentials_exception


def require_role(required_role: UserRole):
    """
    Décorateur pour exiger un rôle spécifique.
    
    Args:
        required_role: Rôle requis
        
    Returns:
        Fonction de dépendance FastAPI
    """
    def role_checker(token_payload: dict = Depends(get_current_user_token)) -> dict:
        user_role = token_payload.get("role")
        
        # Hiérarchie des rôles
        role_hierarchy = {
            UserRole.OPERATOR: 1,
            UserRole.ADMIN: 2,
            UserRole.SUPER_ADMIN: 3,
        }
        
        if not user_role or role_hierarchy.get(UserRole(user_role), 0) < role_hierarchy.get(required_role, 0):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Rôle {required_role.value} requis"
            )
            
        return token_payload
    
    return role_checker


# =============================================================================
# UTILITAIRES DE SÉCURITÉ
# =============================================================================

def generate_api_key() -> str:
    """
    Génère une clé API sécurisée.
    
    Returns:
        str: Clé API
    """
    return secrets.token_urlsafe(32)


def hash_api_key(api_key: str) -> str:
    """
    Hash une clé API pour stockage sécurisé.
    
    Args:
        api_key: Clé API en clair
        
    Returns:
        str: Hash de la clé API
    """
    return hashlib.sha256(api_key.encode()).hexdigest()


def verify_api_key(api_key: str, hashed_key: str) -> bool:
    """
    Vérifie une clé API contre son hash.
    
    Args:
        api_key: Clé API en clair
        hashed_key: Hash de la clé API
        
    Returns:
        bool: True si la clé est valide
    """
    return hmac.compare_digest(
        hashlib.sha256(api_key.encode()).hexdigest(),
        hashed_key
    )


def generate_secure_filename(original_filename: str) -> str:
    """
    Génère un nom de fichier sécurisé.
    
    Args:
        original_filename: Nom de fichier original
        
    Returns:
        str: Nom de fichier sécurisé
    """
    import os
    from pathlib import Path
    
    # Extrait l'extension
    file_extension = Path(original_filename).suffix.lower()
    
    # Génère un nom unique
    secure_name = secrets.token_urlsafe(16)
    
    return f"{secure_name}{file_extension}"


def validate_file_extension(filename: str, allowed_extensions: list) -> bool:
    """
    Valide l'extension d'un fichier.
    
    Args:
        filename: Nom du fichier
        allowed_extensions: Extensions autorisées
        
    Returns:
        bool: True si l'extension est autorisée
    """
    from pathlib import Path
    
    file_extension = Path(filename).suffix.lower().lstrip('.')
    return file_extension in [ext.lower() for ext in allowed_extensions]


# =============================================================================
# CONSTANTES DE SÉCURITÉ
# =============================================================================

# Permissions par rôle
ROLE_PERMISSIONS = {
    UserRole.OPERATOR: [
        "read:dashboard",
        "read:access_logs",
        "create:alerts",
        "read:employees",
    ],
    UserRole.ADMIN: [
        "read:dashboard",
        "read:access_logs",
        "create:alerts",
        "read:employees",
        "create:employees",
        "update:employees",
        "read:users",
        "create:users",
        "update:users",
        "read:readers",
        "update:readers",
        "read:reports",
        "create:reports",
    ],
    UserRole.SUPER_ADMIN: [
        "*",  # Toutes les permissions
    ],
}


def has_permission(user_role: UserRole, permission: str) -> bool:
    """
    Vérifie si un rôle a une permission spécifique.
    
    Args:
        user_role: Rôle de l'utilisateur
        permission: Permission à vérifier
        
    Returns:
        bool: True si la permission est accordée
    """
    permissions = ROLE_PERMISSIONS.get(user_role, [])
    return "*" in permissions or permission in permissions
