# =============================================================================
# PACKAGE MODELS - MODÈLES DE DONNÉES
# =============================================================================

"""
Package contenant tous les modèles SQLAlchemy pour la base de données.

Modèles principaux :
- User : Utilisateurs du système (authentification)
- Employee : Personnel avec badges d'accès
- AccessLog : Historique des passages
- Reader : Configuration des lecteurs RFID
- Alert : Événements d'alerte
- AuditLog : Journalisation des actions
"""

# Import de tous les modèles pour SQLAlchemy
from .user import User
from .employee import Employee
from .access_log import AccessLog
from .reader import Reader
from .alert import Alert
from .audit_log import AuditLog

# Liste des modèles pour l'export
__all__ = [
    "User",
    "Employee", 
    "AccessLog",
    "Reader",
    "Alert",
    "AuditLog",
]
