# =============================================================================
# MODÈLE LOG D'ACCÈS - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Modèle pour l'historique des tentatives d'accès RFID.
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum
from app.models.base import BaseModel


class AccessType(str, Enum):
    """Types d'accès."""
    ENTRY = "entry"                         # Entrée
    EXIT = "exit"                          # Sortie
    <PERSON> = "unknown"                    # Inconnu


class AccessResult(str, Enum):
    """Résultats de tentative d'accès."""
    GRANTED = "granted"                    # Accès accordé
    DENIED = "denied"                      # Accès refusé
    ERROR = "error"                        # Erreur système


class DenialReason(str, Enum):
    """Raisons de refus d'accès."""
    CARD_NOT_FOUND = "card_not_found"              # Carte non trouvée
    CARD_EXPIRED = "card_expired"                  # Carte expirée
    CARD_INACTIVE = "card_inactive"                # Carte inactive
    EMPLOYEE_INACTIVE = "employee_inactive"        # Employé inactif
    EMPLOYEE_SUSPENDED = "employee_suspended"      # Employé suspendu
    ACCESS_DENIED_TIME = "access_denied_time"      # Hors horaires autorisés
    ACCESS_DENIED_ZONE = "access_denied_zone"      # Zone non autorisée
    READER_UNAUTHORIZED = "reader_unauthorized"    # Lecteur non autorisé
    SYSTEM_ERROR = "system_error"                  # Erreur système
    UNKNOWN = "unknown"                            # Raison inconnue


class AccessLog(BaseModel):
    """
    Modèle pour l'historique des accès RFID.
    
    Enregistre toutes les tentatives d'accès, réussies ou échouées,
    pour audit et surveillance.
    """
    __tablename__ = "access_logs"
    
    # ==========================================================================
    # INFORMATIONS DE BASE
    # ==========================================================================
    
    # Timestamp de la tentative d'accès
    access_timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, index=True)
    
    # Type d'accès (entrée/sortie)
    access_type = Column(SQLEnum(AccessType), nullable=False, default=AccessType.UNKNOWN)
    
    # Résultat de la tentative
    result = Column(SQLEnum(AccessResult), nullable=False, index=True)
    
    # Raison du refus (si applicable)
    denial_reason = Column(SQLEnum(DenialReason), nullable=True)
    
    # ==========================================================================
    # INFORMATIONS CARTE/BADGE
    # ==========================================================================
    
    # ID de la carte utilisée
    card_id = Column(String(50), nullable=False, index=True)
    
    # Numéro de série de la carte (si disponible)
    card_serial = Column(String(50), nullable=True)
    
    # Données brutes de la carte
    card_raw_data = Column(Text, nullable=True)
    
    # ==========================================================================
    # INFORMATIONS EMPLOYÉ
    # ==========================================================================
    
    # Référence vers l'employé (peut être NULL si carte non reconnue)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=True, index=True)
    
    # Nom de l'employé au moment de l'accès (pour historique)
    employee_name = Column(String(200), nullable=True)
    
    # Numéro d'employé au moment de l'accès
    employee_number = Column(String(20), nullable=True)
    
    # ==========================================================================
    # INFORMATIONS LECTEUR
    # ==========================================================================
    
    # Référence vers le lecteur
    reader_id = Column(Integer, ForeignKey("readers.id"), nullable=False, index=True)
    
    # Nom du lecteur au moment de l'accès
    reader_name = Column(String(100), nullable=True)
    
    # Localisation du lecteur
    reader_location = Column(String(200), nullable=True)
    
    # Adresse IP du lecteur
    reader_ip = Column(String(45), nullable=True)
    
    # ==========================================================================
    # INFORMATIONS TECHNIQUES
    # ==========================================================================
    
    # Temps de réponse du système (en millisecondes)
    response_time_ms = Column(Integer, nullable=True)
    
    # Version du firmware du lecteur
    reader_firmware = Column(String(50), nullable=True)
    
    # Signal de la carte (force du signal RFID)
    card_signal_strength = Column(Integer, nullable=True)
    
    # Température du lecteur (si disponible)
    reader_temperature = Column(Integer, nullable=True)
    
    # ==========================================================================
    # CONTEXTE ET SÉCURITÉ
    # ==========================================================================
    
    # Événement suspect détecté
    is_suspicious = Column(Boolean, default=False, nullable=False, index=True)
    
    # Raison de suspicion
    suspicious_reason = Column(String(255), nullable=True)
    
    # Marqué manuellement comme suspect
    manually_flagged = Column(Boolean, default=False, nullable=False)
    
    # Utilisateur qui a marqué comme suspect
    flagged_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Date de marquage
    flagged_at = Column(DateTime(timezone=True), nullable=True)
    
    # Notes sur l'événement
    notes = Column(Text, nullable=True)
    
    # ==========================================================================
    # DONNÉES SUPPLÉMENTAIRES
    # ==========================================================================
    
    # Données personnalisées (JSON)
    custom_data = Column(Text, nullable=True)
    
    # Hash pour vérification d'intégrité
    integrity_hash = Column(String(64), nullable=True)
    
    # ==========================================================================
    # RELATIONS
    # ==========================================================================
    
    # Employé concerné
    employee = relationship("Employee", back_populates="access_logs")
    
    # Lecteur utilisé
    reader = relationship("Reader", back_populates="access_logs")
    
    # Utilisateur qui a marqué comme suspect
    flagged_by_user = relationship("User", foreign_keys=[flagged_by_user_id])
    
    # Alertes générées par cet accès
    alerts = relationship("Alert", back_populates="access_log", lazy="dynamic")
    
    # ==========================================================================
    # PROPRIÉTÉS
    # ==========================================================================
    
    @property
    def is_granted(self) -> bool:
        """Vérifie si l'accès a été accordé."""
        return self.result == AccessResult.GRANTED
    
    @property
    def is_denied(self) -> bool:
        """Vérifie si l'accès a été refusé."""
        return self.result == AccessResult.DENIED
    
    @property
    def is_error(self) -> bool:
        """Vérifie s'il y a eu une erreur."""
        return self.result == AccessResult.ERROR
    
    @property
    def is_entry(self) -> bool:
        """Vérifie si c'est une entrée."""
        return self.access_type == AccessType.ENTRY
    
    @property
    def is_exit(self) -> bool:
        """Vérifie si c'est une sortie."""
        return self.access_type == AccessType.EXIT
    
    @property
    def display_result(self) -> str:
        """Résultat formaté pour affichage."""
        result_map = {
            AccessResult.GRANTED: "✅ Accordé",
            AccessResult.DENIED: "❌ Refusé",
            AccessResult.ERROR: "⚠️ Erreur",
        }
        return result_map.get(self.result, "❓ Inconnu")
    
    @property
    def display_type(self) -> str:
        """Type formaté pour affichage."""
        type_map = {
            AccessType.ENTRY: "🔓 Entrée",
            AccessType.EXIT: "🔒 Sortie",
            AccessType.UNKNOWN: "❓ Inconnu",
        }
        return type_map.get(self.access_type, "❓ Inconnu")
    
    # ==========================================================================
    # MÉTHODES
    # ==========================================================================
    
    def flag_as_suspicious(self, reason: str, user_id: int = None):
        """Marque l'événement comme suspect."""
        self.is_suspicious = True
        self.suspicious_reason = reason
        if user_id:
            self.manually_flagged = True
            self.flagged_by_user_id = user_id
            self.flagged_at = func.now()
    
    def unflag_suspicious(self):
        """Retire le marquage suspect."""
        self.is_suspicious = False
        self.suspicious_reason = None
        self.manually_flagged = False
        self.flagged_by_user_id = None
        self.flagged_at = None
    
    def add_note(self, note: str, user_id: int = None):
        """Ajoute une note à l'événement."""
        timestamp = func.now().strftime("%Y-%m-%d %H:%M:%S")
        user_info = f" (User ID: {user_id})" if user_id else ""
        new_note = f"[{timestamp}]{user_info}: {note}"
        
        if self.notes:
            self.notes += f"\n{new_note}"
        else:
            self.notes = new_note
    
    def calculate_integrity_hash(self) -> str:
        """Calcule le hash d'intégrité de l'enregistrement."""
        import hashlib
        
        # Données critiques pour le hash
        data = f"{self.access_timestamp}{self.card_id}{self.result}{self.reader_id}"
        return hashlib.sha256(data.encode()).hexdigest()
    
    def verify_integrity(self) -> bool:
        """Vérifie l'intégrité de l'enregistrement."""
        if not self.integrity_hash:
            return False
        return self.integrity_hash == self.calculate_integrity_hash()
    
    def __repr__(self):
        return (
            f"<AccessLog(id={self.id}, "
            f"card_id='{self.card_id}', "
            f"result='{self.result}', "
            f"timestamp='{self.access_timestamp}')>"
        )
