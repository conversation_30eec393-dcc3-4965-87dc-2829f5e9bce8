# =============================================================================
# MODÈLE DE BASE - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Modèle de base contenant les champs communs à tous les modèles.
"""

from sqlalchemy import Column, Integer, DateTime, Boolean, String
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declared_attr
from app.core.database import Base
import uuid


class BaseModel(Base):
    """
    Modèle de base abstrait avec les champs communs.
    """
    __abstract__ = True
    
    # Clé primaire auto-incrémentée
    id = Column(Integer, primary_key=True, index=True)
    
    # UUID unique pour chaque enregistrement
    uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # Timestamps automatiques
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Soft delete
    is_active = Column(Boolean, default=True, nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    @declared_attr
    def __tablename__(cls):
        """Génère automatiquement le nom de table à partir du nom de classe."""
        return cls.__name__.lower() + 's'
    
    def soft_delete(self):
        """Marque l'enregistrement comme supprimé (soft delete)."""
        self.is_active = False
        self.deleted_at = func.now()
    
    def restore(self):
        """Restaure un enregistrement supprimé."""
        self.is_active = True
        self.deleted_at = None
    
    def to_dict(self):
        """Convertit le modèle en dictionnaire."""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id}, uuid={self.uuid})>"
