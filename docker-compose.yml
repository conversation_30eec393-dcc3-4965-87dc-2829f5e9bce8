version: '3.8'

services:
  # =============================================================================
  # BASE DE DONNÉES POSTGRESQL
  # =============================================================================
  postgres:
    image: postgres:15-alpine
    container_name: secapp_postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-secapp_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secapp_password}
      POSTGRES_DB: ${POSTGRES_DB:-secapp_db}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./database/migrations:/docker-entrypoint-initdb.d/migrations:ro
    networks:
      - secapp_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-secapp_user} -d ${POSTGRES_DB:-secapp_db}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # REDIS (CACHE ET SESSIONS)
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: secapp_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - secapp_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # =============================================================================
  # BACKEND API FASTAPI
  # =============================================================================
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: secapp_backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-secapp_user}:${POSTGRES_PASSWORD:-secapp_password}@postgres:5432/${POSTGRES_DB:-secapp_db}
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./exports:/app/exports
      - ./backups:/app/backups
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - secapp_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # FRONTEND REACT
  # =============================================================================
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: secapp_frontend
    restart: unless-stopped
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000/ws
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - secapp_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # NGINX REVERSE PROXY (PRODUCTION)
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: secapp_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - secapp_network
    profiles:
      - production

  # =============================================================================
  # MONITORING PROMETHEUS (OPTIONNEL)
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: secapp_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - secapp_network
    profiles:
      - monitoring

  # =============================================================================
  # GRAFANA DASHBOARD (OPTIONNEL)
  # =============================================================================
  grafana:
    image: grafana/grafana:latest
    container_name: secapp_grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - secapp_network
    profiles:
      - monitoring

# =============================================================================
# VOLUMES PERSISTANTS
# =============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# =============================================================================
# RÉSEAU
# =============================================================================
networks:
  secapp_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
