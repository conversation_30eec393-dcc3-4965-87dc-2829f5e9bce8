# =============================================================================
# CONFIGURATION LOGGING - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Configuration centralisée du système de logging.
Utilise loguru pour un logging avancé avec rotation et formatage.
"""

import sys
import os
from pathlib import Path
from loguru import logger
import logging
from typing import Dict, Any

from app.core.config import settings


def setup_logging() -> None:
    """
    Configure le système de logging pour l'application.
    """
    # Supprime les handlers par défaut de loguru
    logger.remove()
    
    # Crée le répertoire de logs s'il n'existe pas
    log_dir = Path(settings.LOG_FILE_PATH).parent
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configuration du format de log
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # Handler pour la console (stdout)
    logger.add(
        sys.stdout,
        format=log_format,
        level=settings.LOG_LEVEL,
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # Handler pour le fichier avec rotation
    logger.add(
        settings.LOG_FILE_PATH,
        format=log_format,
        level=settings.LOG_LEVEL,
        rotation=settings.LOG_MAX_SIZE,
        retention=f"{settings.LOG_BACKUP_COUNT} files",
        compression="zip",
        backtrace=True,
        diagnose=True,
        enqueue=True,  # Thread-safe
    )
    
    # Handler spécial pour les erreurs
    error_log_path = str(Path(settings.LOG_FILE_PATH).with_suffix('.error.log'))
    logger.add(
        error_log_path,
        format=log_format,
        level="ERROR",
        rotation=settings.LOG_MAX_SIZE,
        retention=f"{settings.LOG_BACKUP_COUNT * 2} files",
        compression="zip",
        backtrace=True,
        diagnose=True,
        enqueue=True,
    )
    
    # Handler pour l'audit (logs sécurisés)
    audit_log_path = str(Path(settings.LOG_FILE_PATH).with_name('audit.log'))
    logger.add(
        audit_log_path,
        format=(
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
            "AUDIT | "
            "{extra[user_id]:-} | "
            "{extra[action]:-} | "
            "{extra[resource]:-} | "
            "{extra[ip_address]:-} | "
            "{message}"
        ),
        level="INFO",
        rotation="1 day",
        retention=f"{settings.AUDIT_RETENTION_DAYS} days",
        compression="zip",
        filter=lambda record: "audit" in record["extra"],
        enqueue=True,
    )
    
    # Intercepte les logs du module logging standard
    class InterceptHandler(logging.Handler):
        def emit(self, record):
            # Récupère le niveau correspondant de loguru
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno
            
            # Trouve le caller depuis où le log a été émis
            frame, depth = logging.currentframe(), 2
            while frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1
            
            logger.opt(depth=depth, exception=record.exc_info).log(
                level, record.getMessage()
            )
    
    # Configure l'interception des logs standard
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
    
    # Supprime les logs des librairies externes trop verbeux
    logging.getLogger("uvicorn").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    
    logger.info("🔧 Système de logging configuré")


def get_audit_logger():
    """
    Retourne un logger spécialisé pour l'audit.
    
    Returns:
        Logger configuré pour l'audit
    """
    return logger.bind(audit=True)


def log_audit(
    user_id: str,
    action: str,
    resource: str,
    ip_address: str,
    details: str = "",
    **kwargs
) -> None:
    """
    Enregistre un événement d'audit.
    
    Args:
        user_id: ID de l'utilisateur
        action: Action effectuée
        resource: Ressource concernée
        ip_address: Adresse IP
        details: Détails supplémentaires
        **kwargs: Données supplémentaires
    """
    audit_logger = get_audit_logger()
    audit_logger.bind(
        user_id=user_id,
        action=action,
        resource=resource,
        ip_address=ip_address,
        **kwargs
    ).info(details or f"{action} sur {resource}")


def log_access_attempt(
    employee_id: str,
    reader_id: str,
    card_id: str,
    success: bool,
    ip_address: str = "unknown",
    details: str = ""
) -> None:
    """
    Enregistre une tentative d'accès.
    
    Args:
        employee_id: ID de l'employé
        reader_id: ID du lecteur
        card_id: ID de la carte
        success: Succès de l'accès
        ip_address: Adresse IP du lecteur
        details: Détails supplémentaires
    """
    status = "SUCCESS" if success else "DENIED"
    log_audit(
        user_id=employee_id,
        action=f"ACCESS_{status}",
        resource=f"reader_{reader_id}",
        ip_address=ip_address,
        details=details,
        card_id=card_id,
        reader_id=reader_id,
        success=success
    )


def log_user_action(
    user_id: str,
    action: str,
    resource: str,
    ip_address: str,
    details: str = "",
    **kwargs
) -> None:
    """
    Enregistre une action utilisateur dans l'interface.
    
    Args:
        user_id: ID de l'utilisateur
        action: Action effectuée
        resource: Ressource concernée
        ip_address: Adresse IP
        details: Détails supplémentaires
        **kwargs: Données supplémentaires
    """
    log_audit(
        user_id=user_id,
        action=f"USER_{action}",
        resource=resource,
        ip_address=ip_address,
        details=details,
        **kwargs
    )


def log_system_event(
    event_type: str,
    component: str,
    details: str = "",
    **kwargs
) -> None:
    """
    Enregistre un événement système.
    
    Args:
        event_type: Type d'événement
        component: Composant concerné
        details: Détails supplémentaires
        **kwargs: Données supplémentaires
    """
    log_audit(
        user_id="SYSTEM",
        action=f"SYSTEM_{event_type}",
        resource=component,
        ip_address="localhost",
        details=details,
        **kwargs
    )


# Configuration des niveaux de log personnalisés
def add_custom_log_levels():
    """Ajoute des niveaux de log personnalisés."""
    
    # Niveau AUDIT (entre INFO et WARNING)
    logger.level("AUDIT", no=25, color="<blue>")
    
    # Niveau SECURITY (entre WARNING et ERROR)
    logger.level("SECURITY", no=35, color="<red>")
    
    # Niveau ACCESS (entre DEBUG et INFO)
    logger.level("ACCESS", no=15, color="<green>")


# Initialise les niveaux personnalisés
add_custom_log_levels()
