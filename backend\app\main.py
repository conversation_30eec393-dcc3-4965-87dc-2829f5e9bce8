# =============================================================================
# APPLICATION PRINCIPALE FASTAPI - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Point d'entrée principal de l'API FastAPI.
Configure l'application, les middlewares, les routes et les services.
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import time
import logging

from app.core.config import settings
from app.core.database import engine, Base
from app.core.logging import setup_logging
from app.api.api_v1.api import api_router
from app.services.websocket_service import websocket_manager


# Configuration des logs
setup_logging()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Gestionnaire du cycle de vie de l'application.
    Initialise les services au démarrage et nettoie à l'arrêt.
    """
    logger.info("🚀 Démarrage de l'application SecApp")
    
    # Création des tables de base de données
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # Initialisation des services
    await websocket_manager.startup()
    
    logger.info("✅ Application SecApp démarrée avec succès")
    
    yield
    
    # Nettoyage à l'arrêt
    logger.info("🛑 Arrêt de l'application SecApp")
    await websocket_manager.shutdown()
    logger.info("✅ Application SecApp arrêtée proprement")


# =============================================================================
# CRÉATION DE L'APPLICATION FASTAPI
# =============================================================================

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)


# =============================================================================
# MIDDLEWARES
# =============================================================================

# CORS - Configuration pour le frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Trusted Host - Sécurité
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS,
)


# Middleware de timing des requêtes
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Ajoute le temps de traitement dans les headers de réponse."""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# Middleware de logging des requêtes
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log toutes les requêtes pour audit."""
    start_time = time.time()
    
    # Log de la requête entrante
    logger.info(
        f"📥 {request.method} {request.url.path} - "
        f"Client: {request.client.host if request.client else 'Unknown'}"
    )
    
    response = await call_next(request)
    
    # Log de la réponse
    process_time = time.time() - start_time
    logger.info(
        f"📤 {request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )
    
    return response


# =============================================================================
# GESTIONNAIRES D'ERREURS
# =============================================================================

@app.exception_handler(StarletteHTTPException)
async def http_exception_handler(request: Request, exc: StarletteHTTPException):
    """Gestionnaire d'erreurs HTTP personnalisé."""
    logger.error(f"❌ HTTP Error {exc.status_code}: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": True,
            "message": exc.detail,
            "status_code": exc.status_code,
            "timestamp": time.time(),
        },
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Gestionnaire d'erreurs de validation."""
    logger.error(f"❌ Validation Error: {exc.errors()}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": True,
            "message": "Erreur de validation des données",
            "details": exc.errors(),
            "status_code": 422,
            "timestamp": time.time(),
        },
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Gestionnaire d'erreurs générales."""
    logger.error(f"❌ Unexpected Error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": True,
            "message": "Erreur interne du serveur",
            "status_code": 500,
            "timestamp": time.time(),
        },
    )


# =============================================================================
# ROUTES
# =============================================================================

# Routes API principales
app.include_router(api_router, prefix=settings.API_V1_STR)


# Route de santé
@app.get("/health", tags=["Health"])
async def health_check():
    """Point de contrôle de santé de l'application."""
    return {
        "status": "healthy",
        "service": "SecApp Backend API",
        "version": settings.VERSION,
        "timestamp": time.time(),
    }


# Route racine
@app.get("/", tags=["Root"])
async def root():
    """Point d'entrée racine de l'API."""
    return {
        "message": "🔐 SecApp - Système de Contrôle d'Accès",
        "version": settings.VERSION,
        "docs": "/docs",
        "health": "/health",
        "api": settings.API_V1_STR,
    }


# =============================================================================
# POINT D'ENTRÉE
# =============================================================================

if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info",
    )
