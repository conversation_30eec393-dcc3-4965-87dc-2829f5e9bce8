# 🔐 Système de Contrôle d'Accès Multi-Authentification

## 📋 Vue d'ensemble

Système avancé de contrôle d'accès utilisant des lecteurs RFID Hikvision DS-K1T341AM et des codes QR. 
Surveillance temps réel, détection d'intrusions et tableau de bord de supervision complet.

## 🛠️ Stack Technique

- **Backend**: FastAPI (Python)
- **Frontend**: React + Vite
- **Base de données**: PostgreSQL
- **Authentification**: JWT
- **Temps réel**: WebSockets
- **Lecteurs**: Hikvision DS-K1T341AM (protocole ISAPI)
- **Cartes**: MIFARE DESFire EV2

## 🏗️ Architecture

```
Frontend (React) ←→ Backend API (FastAPI) ←→ PostgreSQL 
                                        ↓
                                Service Lecteurs RFID
                                        ↓
                            Lecteurs Hikvision DS-K1T341AM
```

## 📁 Structure du Projet

```
secapp/
├── backend/                 # API FastAPI
│   ├── app/
│   │   ├── api/            # Endpoints REST
│   │   ├── core/           # Configuration, sécurité
│   │   ├── models/         # Modèles SQLAlchemy
│   │   ├── services/       # Logique métier
│   │   └── utils/          # Utilitaires
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/               # React + Vite
│   ├── src/
│   │   ├── components/     # Composants réutilisables
│   │   ├── pages/          # Pages principales
│   │   ├── services/       # API calls
│   │   ├── hooks/          # Custom hooks
│   │   └── utils/          # Utilitaires
│   ├── package.json
│   └── Dockerfile
├── database/
│   ├── migrations/         # Scripts SQL
│   └── init.sql           # Schema initial
├── docker-compose.yml
└── .env.example
```

## 🚀 Démarrage Rapide

### Prérequis
- Docker & Docker Compose
- Node.js 18+
- Python 3.11+

### Installation

1. **Cloner et configurer**
```bash
git clone <repo-url>
cd secapp
cp .env.example .env
```

2. **Démarrer avec Docker**
```bash
docker-compose up -d
```

3. **Accès aux services**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- Documentation API: http://localhost:8000/docs
- PostgreSQL: localhost:5432

## 📊 Fonctionnalités

### ✅ Gestion des accès
- Lecture automatique badges RFID et codes QR
- Validation temps réel des autorisations
- Enregistrement automatique des passages
- Gestion des profils d'accès par employé

### ✅ Tableau de bord temps réel
- Liste des personnes présentes sur site
- Derniers passages en temps réel
- Statut des lecteurs (en ligne/hors ligne)
- Alertes visuelles pour événements suspects
- Statistiques de fréquentation

### ✅ Système d'alertes
- Détection automatique de cartes non autorisées
- Marquage manuel d'événements suspects
- Notifications visuelles dans l'interface
- Archivage des événements d'alerte

### ✅ Gestion des utilisateurs (3 niveaux)
- **Opérateur**: Surveillance, marquage d'événements
- **Administrateur**: Gestion système et utilisateurs
- **Super Administrateur**: Accès complet + archives

### ✅ Historique et rapports
- Consultation filtrable par date/employé/porte
- Export des données (CSV, PDF)
- Recherche avancée dans l'historique
- Rapports de présence et statistiques

### ✅ Sécurité et audit
- Journalisation obligatoire de toutes les actions
- Authentification JWT sécurisée
- Audit trail complet des connexions
- Monitoring des tentatives d'accès

## 🔧 Configuration

### Variables d'environnement
Voir `.env.example` pour la configuration complète.

### Lecteurs Hikvision
Configuration ISAPI dans `backend/app/core/config.py`

## 📚 Documentation

- [Guide d'installation](docs/installation.md)
- [Documentation API](http://localhost:8000/docs)
- [Manuel utilisateur](docs/user-guide.md)
- [Guide développeur](docs/developer-guide.md)

## 🧪 Tests

```bash
# Backend
cd backend
pytest

# Frontend
cd frontend
npm test
```

## 📈 Monitoring

- Logs: `docker-compose logs -f`
- Métriques: Intégration Prometheus/Grafana (à venir)
- Alertes: Configuration SMTP pour notifications

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir `LICENSE` pour plus de détails.

## 📞 Support

- Email: <EMAIL>
- Documentation: [Wiki du projet](wiki-url)
- Issues: [GitHub Issues](issues-url)
