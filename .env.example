# =============================================================================
# CONFIGURATION ENVIRONNEMENT - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

# -----------------------------------------------------------------------------
# BASE DE DONNÉES POSTGRESQL
# -----------------------------------------------------------------------------
DATABASE_URL=postgresql://secapp_user:secapp_password@localhost:5432/secapp_db
POSTGRES_USER=secapp_user
POSTGRES_PASSWORD=secapp_password
POSTGRES_DB=secapp_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# -----------------------------------------------------------------------------
# CONFIGURATION API BACKEND
# -----------------------------------------------------------------------------
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=true
API_RELOAD=true

# -----------------------------------------------------------------------------
# SÉCURITÉ JWT
# -----------------------------------------------------------------------------
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# -----------------------------------------------------------------------------
# CONFIGURATION FRONTEND
# -----------------------------------------------------------------------------
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000/ws
VITE_APP_TITLE=SecApp - Contrôle d'Accès

# -----------------------------------------------------------------------------
# LECTEURS HIKVISION ISAPI
# -----------------------------------------------------------------------------
HIKVISION_DEFAULT_USERNAME=admin
HIKVISION_DEFAULT_PASSWORD=admin123
HIKVISION_TIMEOUT=30
HIKVISION_MAX_RETRIES=3

# Configuration des lecteurs (format JSON)
# HIKVISION_READERS=[{"id": "reader_01", "ip": "*************", "port": 80, "username": "admin", "password": "admin123", "location": "Entrée principale"}]

# -----------------------------------------------------------------------------
# WEBSOCKETS TEMPS RÉEL
# -----------------------------------------------------------------------------
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100

# -----------------------------------------------------------------------------
# SYSTÈME D'ALERTES
# -----------------------------------------------------------------------------
ALERT_EMAIL_ENABLED=false
ALERT_EMAIL_SMTP_HOST=smtp.gmail.com
ALERT_EMAIL_SMTP_PORT=587
ALERT_EMAIL_USERNAME=<EMAIL>
ALERT_EMAIL_PASSWORD=your-email-password
ALERT_EMAIL_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>

# -----------------------------------------------------------------------------
# LOGS ET AUDIT
# -----------------------------------------------------------------------------
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/secapp.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5
AUDIT_RETENTION_DAYS=365

# -----------------------------------------------------------------------------
# REDIS (OPTIONNEL - POUR CACHE ET SESSIONS)
# -----------------------------------------------------------------------------
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# -----------------------------------------------------------------------------
# CONFIGURATION CORS
# -----------------------------------------------------------------------------
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# -----------------------------------------------------------------------------
# CONFIGURATION UPLOADS
# -----------------------------------------------------------------------------
UPLOAD_MAX_SIZE=10MB
UPLOAD_ALLOWED_EXTENSIONS=["jpg", "jpeg", "png", "pdf"]
UPLOAD_PATH=uploads/

# -----------------------------------------------------------------------------
# CONFIGURATION RAPPORTS
# -----------------------------------------------------------------------------
REPORTS_EXPORT_PATH=exports/
REPORTS_MAX_RECORDS=10000
REPORTS_CACHE_TTL=300

# -----------------------------------------------------------------------------
# MONITORING ET PERFORMANCE
# -----------------------------------------------------------------------------
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=60

# -----------------------------------------------------------------------------
# ENVIRONNEMENT
# -----------------------------------------------------------------------------
ENVIRONMENT=development
DEBUG=true
TESTING=false

# -----------------------------------------------------------------------------
# CONFIGURATION DOCKER
# -----------------------------------------------------------------------------
COMPOSE_PROJECT_NAME=secapp
DOCKER_BUILDKIT=1

# -----------------------------------------------------------------------------
# BACKUP ET MAINTENANCE
# -----------------------------------------------------------------------------
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=backups/

# -----------------------------------------------------------------------------
# CONFIGURATION TIMEZONE
# -----------------------------------------------------------------------------
TZ=Europe/Paris
LOCALE=fr_FR.UTF-8
