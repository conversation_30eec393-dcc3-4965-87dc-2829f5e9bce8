# =============================================================================
# PACKAGE PRINCIPAL BACKEND - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Backend API FastAPI pour système de contrôle d'accès multi-authentification.

Ce package contient tous les modules nécessaires pour :
- Authentification JWT sécurisée
- Gestion des utilisateurs et employés
- Communication avec lecteurs RFID Hikvision
- Surveillance temps réel des accès
- Système d'alertes et audit
- Génération de rapports

Architecture :
- api/ : Endpoints REST API
- core/ : Configuration et sécurité
- models/ : Modèles de données SQLAlchemy
- services/ : Logique métier
- utils/ : Utilitaires et helpers
"""

__version__ = "1.0.0"
__author__ = "SecApp Team"
__description__ = "Système de contrôle d'accès multi-authentification"
