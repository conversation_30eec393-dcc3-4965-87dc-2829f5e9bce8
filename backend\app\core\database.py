# =============================================================================
# CONFIGURATION BASE DE DONNÉES - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Configuration de la base de données PostgreSQL avec SQLAlchemy.
Support des connexions synchrones et asynchrones.
"""

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from typing import AsyncGenerator, Generator
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# =============================================================================
# CONFIGURATION SQLALCHEMY
# =============================================================================

# Métadonnées pour les contraintes de nommage
convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

metadata = MetaData(naming_convention=convention)

# Base pour les modèles
Base = declarative_base(metadata=metadata)

# =============================================================================
# MOTEURS DE BASE DE DONNÉES
# =============================================================================

# Moteur synchrone
engine = create_engine(
    settings.database_url_sync,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
    echo=settings.DEBUG,
)

# Moteur asynchrone
async_engine = create_async_engine(
    settings.database_url_async,
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=10,
    max_overflow=20,
    echo=settings.DEBUG,
)

# =============================================================================
# SESSIONS
# =============================================================================

# Session synchrone
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
)

# Session asynchrone
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
)

# =============================================================================
# DÉPENDANCES POUR FASTAPI
# =============================================================================

def get_db() -> Generator[Session, None, None]:
    """
    Dépendance FastAPI pour obtenir une session de base de données synchrone.
    
    Yields:
        Session: Session SQLAlchemy synchrone
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Erreur de session de base de données: {e}")
        db.rollback()
        raise
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dépendance FastAPI pour obtenir une session de base de données asynchrone.
    
    Yields:
        AsyncSession: Session SQLAlchemy asynchrone
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Erreur de session de base de données asynchrone: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


# =============================================================================
# UTILITAIRES DE BASE DE DONNÉES
# =============================================================================

async def init_db() -> None:
    """
    Initialise la base de données en créant toutes les tables.
    """
    try:
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("✅ Base de données initialisée avec succès")
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'initialisation de la base de données: {e}")
        raise


async def drop_db() -> None:
    """
    Supprime toutes les tables de la base de données.
    ⚠️ ATTENTION: Cette fonction supprime toutes les données !
    """
    try:
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        logger.warning("⚠️ Toutes les tables ont été supprimées")
    except Exception as e:
        logger.error(f"❌ Erreur lors de la suppression des tables: {e}")
        raise


async def check_db_connection() -> bool:
    """
    Vérifie la connexion à la base de données.
    
    Returns:
        bool: True si la connexion est OK, False sinon
    """
    try:
        async with async_engine.begin() as conn:
            await conn.execute("SELECT 1")
        logger.info("✅ Connexion à la base de données OK")
        return True
    except Exception as e:
        logger.error(f"❌ Erreur de connexion à la base de données: {e}")
        return False


def create_tables() -> None:
    """
    Crée toutes les tables de manière synchrone.
    Utilisé principalement pour les tests et l'initialisation.
    """
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Tables créées avec succès (mode synchrone)")
    except Exception as e:
        logger.error(f"❌ Erreur lors de la création des tables: {e}")
        raise


def drop_tables() -> None:
    """
    Supprime toutes les tables de manière synchrone.
    ⚠️ ATTENTION: Cette fonction supprime toutes les données !
    """
    try:
        Base.metadata.drop_all(bind=engine)
        logger.warning("⚠️ Toutes les tables ont été supprimées (mode synchrone)")
    except Exception as e:
        logger.error(f"❌ Erreur lors de la suppression des tables: {e}")
        raise


# =============================================================================
# CONTEXTE MANAGER POUR TRANSACTIONS
# =============================================================================

class DatabaseTransaction:
    """
    Context manager pour gérer les transactions de base de données.
    """
    
    def __init__(self, session: Session):
        self.session = session
    
    def __enter__(self):
        return self.session
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.session.rollback()
            logger.error(f"Transaction annulée: {exc_val}")
        else:
            self.session.commit()
            logger.debug("Transaction validée")


class AsyncDatabaseTransaction:
    """
    Context manager asynchrone pour gérer les transactions de base de données.
    """
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def __aenter__(self):
        return self.session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.session.rollback()
            logger.error(f"Transaction asynchrone annulée: {exc_val}")
        else:
            await self.session.commit()
            logger.debug("Transaction asynchrone validée")
