# =============================================================================
# MODÈLE UTILISATEUR - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Modèle pour les utilisateurs du système (authentification interface web).
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum
from app.models.base import BaseModel


class UserRole(str, Enum):
    """Rôles utilisateur du système."""
    OPERATOR = "operator"                    # Opérateur - Surveillance
    ADMIN = "admin"                         # Administrateur - Gestion
    SUPER_ADMIN = "super_admin"             # Super Admin - Accès complet


class UserStatus(str, Enum):
    """Statuts utilisateur."""
    ACTIVE = "active"                       # Actif
    INACTIVE = "inactive"                   # Inactif
    SUSPENDED = "suspended"                 # Suspendu
    LOCKED = "locked"                       # Verrouillé


class User(BaseModel):
    """
    Modèle utilisateur pour l'authentification système.
    
    Représente les utilisateurs qui peuvent se connecter à l'interface web
    pour surveiller et gérer le système de contrôle d'accès.
    """
    __tablename__ = "users"
    
    # ==========================================================================
    # INFORMATIONS PERSONNELLES
    # ==========================================================================
    
    # Nom d'utilisateur unique
    username = Column(String(50), unique=True, index=True, nullable=False)
    
    # Email unique
    email = Column(String(255), unique=True, index=True, nullable=False)
    
    # Mot de passe haché
    password_hash = Column(String(255), nullable=False)
    
    # Nom complet
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    
    # Téléphone
    phone = Column(String(20), nullable=True)
    
    # ==========================================================================
    # RÔLE ET PERMISSIONS
    # ==========================================================================
    
    # Rôle utilisateur
    role = Column(SQLEnum(UserRole), nullable=False, default=UserRole.OPERATOR)
    
    # Statut du compte
    status = Column(SQLEnum(UserStatus), nullable=False, default=UserStatus.ACTIVE)
    
    # Permissions spéciales (JSON)
    permissions = Column(Text, nullable=True)  # JSON string
    
    # ==========================================================================
    # SÉCURITÉ
    # ==========================================================================
    
    # Email vérifié
    email_verified = Column(Boolean, default=False, nullable=False)
    
    # Date de vérification email
    email_verified_at = Column(DateTime(timezone=True), nullable=True)
    
    # Dernière connexion
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    
    # Adresse IP dernière connexion
    last_login_ip = Column(String(45), nullable=True)  # IPv6 compatible
    
    # Tentatives de connexion échouées
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    
    # Date de verrouillage du compte
    locked_at = Column(DateTime(timezone=True), nullable=True)
    
    # Token de réinitialisation mot de passe
    reset_password_token = Column(String(255), nullable=True)
    
    # Expiration token réinitialisation
    reset_password_expires = Column(DateTime(timezone=True), nullable=True)
    
    # ==========================================================================
    # CONFIGURATION UTILISATEUR
    # ==========================================================================
    
    # Langue préférée
    preferred_language = Column(String(5), default="fr", nullable=False)
    
    # Fuseau horaire
    timezone = Column(String(50), default="Europe/Paris", nullable=False)
    
    # Notifications email activées
    email_notifications = Column(Boolean, default=True, nullable=False)
    
    # Thème interface (dark/light)
    theme = Column(String(20), default="light", nullable=False)
    
    # ==========================================================================
    # RELATIONS
    # ==========================================================================
    
    # Logs d'audit créés par cet utilisateur
    audit_logs = relationship("AuditLog", back_populates="user", lazy="dynamic")
    
    # Alertes créées par cet utilisateur
    alerts_created = relationship(
        "Alert", 
        foreign_keys="Alert.created_by_user_id",
        back_populates="created_by_user", 
        lazy="dynamic"
    )
    
    # Alertes assignées à cet utilisateur
    alerts_assigned = relationship(
        "Alert",
        foreign_keys="Alert.assigned_to_user_id", 
        back_populates="assigned_to_user",
        lazy="dynamic"
    )
    
    # ==========================================================================
    # PROPRIÉTÉS
    # ==========================================================================
    
    @property
    def full_name(self) -> str:
        """Nom complet de l'utilisateur."""
        return f"{self.first_name} {self.last_name}"
    
    @property
    def is_admin(self) -> bool:
        """Vérifie si l'utilisateur est administrateur."""
        return self.role in [UserRole.ADMIN, UserRole.SUPER_ADMIN]
    
    @property
    def is_super_admin(self) -> bool:
        """Vérifie si l'utilisateur est super administrateur."""
        return self.role == UserRole.SUPER_ADMIN
    
    @property
    def is_locked(self) -> bool:
        """Vérifie si le compte est verrouillé."""
        return self.status == UserStatus.LOCKED or self.locked_at is not None
    
    @property
    def is_account_active(self) -> bool:
        """Vérifie si le compte est actif."""
        return (
            self.is_active and 
            self.status == UserStatus.ACTIVE and 
            not self.is_locked
        )
    
    # ==========================================================================
    # MÉTHODES
    # ==========================================================================
    
    def lock_account(self):
        """Verrouille le compte utilisateur."""
        self.status = UserStatus.LOCKED
        self.locked_at = func.now()
    
    def unlock_account(self):
        """Déverrouille le compte utilisateur."""
        self.status = UserStatus.ACTIVE
        self.locked_at = None
        self.failed_login_attempts = 0
    
    def increment_failed_login(self):
        """Incrémente le compteur de tentatives échouées."""
        self.failed_login_attempts += 1
        
        # Verrouillage automatique après 5 tentatives
        if self.failed_login_attempts >= 5:
            self.lock_account()
    
    def reset_failed_login(self):
        """Remet à zéro le compteur de tentatives échouées."""
        self.failed_login_attempts = 0
    
    def update_last_login(self, ip_address: str = None):
        """Met à jour les informations de dernière connexion."""
        self.last_login_at = func.now()
        if ip_address:
            self.last_login_ip = ip_address
        self.reset_failed_login()
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"
