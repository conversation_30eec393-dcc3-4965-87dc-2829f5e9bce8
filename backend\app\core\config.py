# =============================================================================
# CONFIGURATION PRINCIPALE - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

"""
Configuration centralisée de l'application utilisant Pydantic Settings.
Gère toutes les variables d'environnement et paramètres de configuration.
"""

from typing import List, Optional, Union, Dict, Any
from pydantic import BaseSettings, validator, Field
from pydantic_settings import BaseSettings
import secrets
import os


class Settings(BaseSettings):
    """Configuration principale de l'application."""
    
    # ==========================================================================
    # INFORMATIONS GÉNÉRALES
    # ==========================================================================
    PROJECT_NAME: str = "SecApp - Système de Contrôle d'Accès"
    PROJECT_DESCRIPTION: str = "API FastAPI pour système de contrôle d'accès multi-authentification avec lecteurs RFID Hikvision"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # ==========================================================================
    # SERVEUR
    # ==========================================================================
    HOST: str = Field(default="0.0.0.0", env="API_HOST")
    PORT: int = Field(default=8000, env="API_PORT")
    DEBUG: bool = Field(default=False, env="API_DEBUG")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    
    # ==========================================================================
    # SÉCURITÉ JWT
    # ==========================================================================
    SECRET_KEY: str = Field(default_factory=lambda: secrets.token_urlsafe(32))
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    @validator("SECRET_KEY", pre=True)
    def validate_secret_key(cls, v):
        if v == "your-super-secret-jwt-key-change-this-in-production":
            if os.getenv("ENVIRONMENT") == "production":
                raise ValueError("SECRET_KEY must be changed in production!")
        return v
    
    # ==========================================================================
    # BASE DE DONNÉES POSTGRESQL
    # ==========================================================================
    DATABASE_URL: str = Field(
        default="postgresql://secapp_user:secapp_password@localhost:5432/secapp_db",
        env="DATABASE_URL"
    )
    POSTGRES_HOST: str = Field(default="localhost", env="POSTGRES_HOST")
    POSTGRES_PORT: int = Field(default=5432, env="POSTGRES_PORT")
    POSTGRES_USER: str = Field(default="secapp_user", env="POSTGRES_USER")
    POSTGRES_PASSWORD: str = Field(default="secapp_password", env="POSTGRES_PASSWORD")
    POSTGRES_DB: str = Field(default="secapp_db", env="POSTGRES_DB")
    
    # ==========================================================================
    # REDIS CACHE
    # ==========================================================================
    REDIS_URL: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    REDIS_HOST: str = Field(default="localhost", env="REDIS_HOST")
    REDIS_PORT: int = Field(default=6379, env="REDIS_PORT")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # ==========================================================================
    # CORS ET SÉCURITÉ
    # ==========================================================================
    BACKEND_CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="CORS_ORIGINS"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1", "*"],
        env="ALLOWED_HOSTS"
    )
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # ==========================================================================
    # LECTEURS HIKVISION ISAPI
    # ==========================================================================
    HIKVISION_DEFAULT_USERNAME: str = Field(default="admin", env="HIKVISION_DEFAULT_USERNAME")
    HIKVISION_DEFAULT_PASSWORD: str = Field(default="admin123", env="HIKVISION_DEFAULT_PASSWORD")
    HIKVISION_TIMEOUT: int = Field(default=30, env="HIKVISION_TIMEOUT")
    HIKVISION_MAX_RETRIES: int = Field(default=3, env="HIKVISION_MAX_RETRIES")
    HIKVISION_READERS: Optional[str] = Field(default=None, env="HIKVISION_READERS")
    
    # ==========================================================================
    # WEBSOCKETS
    # ==========================================================================
    WS_HEARTBEAT_INTERVAL: int = Field(default=30, env="WS_HEARTBEAT_INTERVAL")
    WS_MAX_CONNECTIONS: int = Field(default=100, env="WS_MAX_CONNECTIONS")
    
    # ==========================================================================
    # SYSTÈME D'ALERTES EMAIL
    # ==========================================================================
    ALERT_EMAIL_ENABLED: bool = Field(default=False, env="ALERT_EMAIL_ENABLED")
    ALERT_EMAIL_SMTP_HOST: str = Field(default="smtp.gmail.com", env="ALERT_EMAIL_SMTP_HOST")
    ALERT_EMAIL_SMTP_PORT: int = Field(default=587, env="ALERT_EMAIL_SMTP_PORT")
    ALERT_EMAIL_USERNAME: Optional[str] = Field(default=None, env="ALERT_EMAIL_USERNAME")
    ALERT_EMAIL_PASSWORD: Optional[str] = Field(default=None, env="ALERT_EMAIL_PASSWORD")
    ALERT_EMAIL_FROM: Optional[str] = Field(default=None, env="ALERT_EMAIL_FROM")
    ALERT_EMAIL_TO: Optional[str] = Field(default=None, env="ALERT_EMAIL_TO")
    
    # ==========================================================================
    # LOGS ET AUDIT
    # ==========================================================================
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE_PATH: str = Field(default="logs/secapp.log", env="LOG_FILE_PATH")
    LOG_MAX_SIZE: str = Field(default="10MB", env="LOG_MAX_SIZE")
    LOG_BACKUP_COUNT: int = Field(default=5, env="LOG_BACKUP_COUNT")
    AUDIT_RETENTION_DAYS: int = Field(default=365, env="AUDIT_RETENTION_DAYS")
    
    # ==========================================================================
    # UPLOADS ET FICHIERS
    # ==========================================================================
    UPLOAD_MAX_SIZE: str = Field(default="10MB", env="UPLOAD_MAX_SIZE")
    UPLOAD_ALLOWED_EXTENSIONS: List[str] = Field(
        default=["jpg", "jpeg", "png", "pdf"],
        env="UPLOAD_ALLOWED_EXTENSIONS"
    )
    UPLOAD_PATH: str = Field(default="uploads/", env="UPLOAD_PATH")
    
    # ==========================================================================
    # RAPPORTS
    # ==========================================================================
    REPORTS_EXPORT_PATH: str = Field(default="exports/", env="REPORTS_EXPORT_PATH")
    REPORTS_MAX_RECORDS: int = Field(default=10000, env="REPORTS_MAX_RECORDS")
    REPORTS_CACHE_TTL: int = Field(default=300, env="REPORTS_CACHE_TTL")
    
    # ==========================================================================
    # MONITORING
    # ==========================================================================
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    METRICS_PORT: int = Field(default=9090, env="METRICS_PORT")
    HEALTH_CHECK_INTERVAL: int = Field(default=60, env="HEALTH_CHECK_INTERVAL")
    
    # ==========================================================================
    # BACKUP
    # ==========================================================================
    BACKUP_ENABLED: bool = Field(default=True, env="BACKUP_ENABLED")
    BACKUP_SCHEDULE: str = Field(default="0 2 * * *", env="BACKUP_SCHEDULE")
    BACKUP_RETENTION_DAYS: int = Field(default=30, env="BACKUP_RETENTION_DAYS")
    BACKUP_PATH: str = Field(default="backups/", env="BACKUP_PATH")
    
    # ==========================================================================
    # TIMEZONE
    # ==========================================================================
    TZ: str = Field(default="Europe/Paris", env="TZ")
    LOCALE: str = Field(default="fr_FR.UTF-8", env="LOCALE")
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        
    def get_hikvision_readers(self) -> List[Dict[str, Any]]:
        """Parse et retourne la configuration des lecteurs Hikvision."""
        if not self.HIKVISION_READERS:
            return []
        
        try:
            import json
            return json.loads(self.HIKVISION_READERS)
        except (json.JSONDecodeError, TypeError):
            return []
    
    @property
    def database_url_sync(self) -> str:
        """URL de base de données synchrone pour SQLAlchemy."""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+psycopg2://")
    
    @property
    def database_url_async(self) -> str:
        """URL de base de données asynchrone pour SQLAlchemy."""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")


# Instance globale des paramètres
settings = Settings()
