# =============================================================================
# DÉPENDANCES BACKEND - SYSTÈME DE CONTRÔLE D'ACCÈS
# =============================================================================

# -----------------------------------------------------------------------------
# FRAMEWORK WEB
# -----------------------------------------------------------------------------
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# -----------------------------------------------------------------------------
# BASE DE DONNÉES
# -----------------------------------------------------------------------------
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# -----------------------------------------------------------------------------
# AUTHENTIFICATION ET SÉCURITÉ
# -----------------------------------------------------------------------------
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# -----------------------------------------------------------------------------
# VALIDATION ET SÉRIALISATION
# -----------------------------------------------------------------------------
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# -----------------------------------------------------------------------------
# WEBSOCKETS TEMPS RÉEL
# -----------------------------------------------------------------------------
websockets==12.0
python-socketio==5.10.0

# -----------------------------------------------------------------------------
# CACHE ET SESSIONS
# -----------------------------------------------------------------------------
redis==5.0.1
aioredis==2.0.1

# -----------------------------------------------------------------------------
# COMMUNICATION HTTP
# -----------------------------------------------------------------------------
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# -----------------------------------------------------------------------------
# TRAITEMENT DES DONNÉES
# -----------------------------------------------------------------------------
pandas==2.1.4
numpy==1.26.2
openpyxl==3.1.2

# -----------------------------------------------------------------------------
# GÉNÉRATION DE RAPPORTS
# -----------------------------------------------------------------------------
reportlab==4.0.7
jinja2==3.1.2
weasyprint==60.2

# -----------------------------------------------------------------------------
# LOGS ET MONITORING
# -----------------------------------------------------------------------------
loguru==0.7.2
prometheus-client==0.19.0
structlog==23.2.0

# -----------------------------------------------------------------------------
# TÂCHES ASYNCHRONES
# -----------------------------------------------------------------------------
celery==5.3.4
kombu==5.3.4

# -----------------------------------------------------------------------------
# CONFIGURATION
# -----------------------------------------------------------------------------
python-dotenv==1.0.0
pyyaml==6.0.1
toml==0.10.2

# -----------------------------------------------------------------------------
# UTILITAIRES
# -----------------------------------------------------------------------------
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7
rich==13.7.0

# -----------------------------------------------------------------------------
# TESTS
# -----------------------------------------------------------------------------
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# -----------------------------------------------------------------------------
# DÉVELOPPEMENT
# -----------------------------------------------------------------------------
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# -----------------------------------------------------------------------------
# COMMUNICATION HIKVISION ISAPI
# -----------------------------------------------------------------------------
xmltodict==0.13.0
lxml==4.9.3

# -----------------------------------------------------------------------------
# SÉCURITÉ ADDITIONNELLE
# -----------------------------------------------------------------------------
cryptography==41.0.8
pyotp==2.9.0

# -----------------------------------------------------------------------------
# GESTION DES IMAGES
# -----------------------------------------------------------------------------
pillow==10.1.0
qrcode[pil]==7.4.2

# -----------------------------------------------------------------------------
# VALIDATION AVANCÉE
# -----------------------------------------------------------------------------
cerberus==1.3.5
marshmallow==3.20.2
